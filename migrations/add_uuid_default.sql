-- Simple migration to add UUID() as default for existing VARCHAR(36) id column

-- Modify the existing id column to have UUID() as default
ALTER TABLE users MODIFY COLUMN id VARCHAR(36) NOT NULL DEFAULT (UUID());

-- If you want to update existing records with new UUIDs (be careful with this!)
-- UPDATE users SET id = UUID() WHERE id IS NULL OR id = '';

-- Create the updated table definition for reference:
/*
create table users
(
    id            varchar(36)                          default (UUID())           not null
        primary key,
    phone         varchar(11)                                                     null,
    employee_id   varchar(50)                                                     null,
    email         varchar(255)                                                    null,
    password_hash varchar(255)                                                    not null,
    role          enum ('user', 'admin')               default 'user'             not null,
    status        enum ('active', 'frozen', 'deleted') default 'active'           not null,
    created_at    timestamp                            default CURRENT_TIMESTAMP  null,
    updated_at    timestamp                            default CURRENT_TIMESTAMP  null on update CURRENT_TIMESTAMP,
    constraint email
        unique (email),
    constraint employee_id
        unique (employee_id),
    constraint phone
        unique (phone)
)
    engine = InnoDB;
*/
