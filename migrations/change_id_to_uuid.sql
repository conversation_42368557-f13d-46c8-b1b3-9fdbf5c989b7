-- Migration to change id from VARCHAR(36) to BINARY(16) with UUID
-- This provides better performance and storage efficiency

-- Step 1: Create a new temporary column
ALTER TABLE users ADD COLUMN id_new BINARY(16) NOT NULL DEFAULT (UUID_TO_BIN(UUID()));

-- Step 2: Update the new column with converted existing IDs (if they are valid UUIDs)
-- If existing IDs are already UUIDs in string format:
UPDATE users SET id_new = UUID_TO_BIN(id) WHERE id REGEXP '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$';

-- If existing IDs are not UUIDs, generate new ones:
-- UPDATE users SET id_new = UUID_TO_BIN(UUID());

-- Step 3: Drop the old primary key
ALTER TABLE users DROP PRIMARY KEY;

-- Step 4: Drop the old id column
ALTER TABLE users DROP COLUMN id;

-- Step 5: Rename the new column to id
ALTER TABLE users CHANGE COLUMN id_new id BINARY(16) NOT NULL;

-- Step 6: Add the primary key back
ALTER TABLE users ADD PRIMARY KEY (id);

-- Step 7: Update related tables (borrowings, etc.) to use BINARY(16) as well
-- You'll need to do similar changes for foreign key references

-- Alternative: If you want to keep VARCHAR but ensure UUID format
-- ALTER TABLE users MODIFY COLUMN id VARCHAR(36) NOT NULL DEFAULT (UUID());
