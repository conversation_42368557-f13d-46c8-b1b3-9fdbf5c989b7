-- Updated users table definition with proper UUID handling

-- Drop existing table (use with caution!)
-- DROP TABLE IF EXISTS users;

-- Create new table with <PERSON>INARY(16) for UUID (most efficient)
create table users
(
    id            BINARY(16)                           default (UUID_TO_BIN(UUID())) not null
        primary key,
    phone         varchar(11)                                                         null,
    employee_id   varchar(50)                                                         null,
    email         varchar(255)                                                        null,
    password_hash varchar(255)                                                        not null,
    role          enum ('user', 'admin')               default 'user'                 not null,
    status        enum ('active', 'frozen', 'deleted') default 'active'               not null,
    created_at    timestamp                            default CURRENT_TIMESTAMP      null,
    updated_at    timestamp                            default CURRENT_TIMESTAMP      null on update CURRENT_TIMESTAMP,
    constraint email
        unique (email),
    constraint employee_id
        unique (employee_id),
    constraint phone
        unique (phone)
)
    engine = InnoDB;

create index idx_email
    on users (email);

create index idx_employee_id
    on users (employee_id);

create index idx_phone
    on users (phone);

-- Alternative with VARCHAR(36) and UUID() default
/*
create table users
(
    id            varchar(36)                          default (UUID())            not null
        primary key,
    phone         varchar(11)                                                      null,
    employee_id   varchar(50)                                                      null,
    email         varchar(255)                                                     null,
    password_hash varchar(255)                                                     not null,
    role          enum ('user', 'admin')               default 'user'              not null,
    status        enum ('active', 'frozen', 'deleted') default 'active'            not null,
    created_at    timestamp                            default CURRENT_TIMESTAMP   null,
    updated_at    timestamp                            default CURRENT_TIMESTAMP   null on update CURRENT_TIMESTAMP,
    constraint email
        unique (email),
    constraint employee_id
        unique (employee_id),
    constraint phone
        unique (phone)
)
    engine = InnoDB;
*/
