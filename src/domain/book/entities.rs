use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>ialEq, DeriveEntityModel, Eq)]
#[sea_orm(table_name = "books")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: String,
    pub title: String,
    pub author: String,
    pub isbn: String,
    pub publisher: String,
    pub total_quantity: i32,
    pub available_quantity: i32,
    pub borrowable: i8,
    pub created_at: Option<DateTimeUtc>,
    pub updated_at: Option<DateTimeUtc>,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::borrowings::Entity")]
    Borrowings,
}

impl Related<super::super::borrowings::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Borrowings.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
