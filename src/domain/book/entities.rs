use sea_orm::entity::prelude::*;
use chrono::{DateTime, Utc};
use serde::Serialize;

type DateTimeUtc = DateTime<Utc>;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "books")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: String,
    pub title: String,
    pub author: String,
    pub isbn: String,
    pub publisher: String,
    pub total_quantity: i32,
    pub available_quantity: i32,
    pub borrowable: i8,
    pub created_at: Option<DateTimeUtc>,
    pub updated_at: Option<DateTimeUtc>,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::super::borrowing::entities::Entity")]
    Borrowings,
}

impl Related<super::super::borrowing::entities::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Borrowings.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
