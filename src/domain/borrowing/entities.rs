use sea_orm::entity::prelude::*;
use chrono::{DateTime, Utc};
use serde::Serialize;

type DateTimeUtc = DateTime<Utc>;

#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "status")]
pub enum Status {
    #[sea_orm(string_value = "borrowed")]
    Borrowed,
    #[sea_orm(string_value = "returned")]
    Returned,
    #[sea_orm(string_value = "overdue")]
    Overdue,
}

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq)]
#[sea_orm(table_name = "borrowings")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: String,
    pub user_id: String,
    pub book_id: String,
    pub borrowed_at: Option<DateTimeUtc>,
    pub due_date: DateTimeUtc,
    pub returned_at: Option<DateTimeUtc>,
    pub status: Status,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::super::book::entities::Entity",
        from = "Column::BookId",
        to = "super::super::book::entities::Column::Id",
        on_update = "NoAction",
        on_delete = "Cascade"
    )]
    Books,
    #[sea_orm(
        belongs_to = "super::super::user::entities::Entity",
        from = "Column::UserId",
        to = "super::super::user::entities::Column::Id",
        on_update = "NoAction",
        on_delete = "Cascade"
    )]
    Users,
}

impl Related<super::super::book::entities::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Books.def()
    }
}

impl Related<super::super::user::entities::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Users.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
