use super::sea_orm_active_enums::Status;
use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, DeriveEntityModel, Eq)]
#[sea_orm(table_name = "borrowings")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: String,
    pub user_id: String,
    pub book_id: String,
    pub borrowed_at: Option<DateTimeUtc>,
    pub due_date: DateTimeUtc,
    pub returned_at: Option<DateTimeUtc>,
    pub status: Status,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::books::Entity",
        from = "Column::BookId",
        to = "super::books::Column::Id",
        on_update = "NoAction",
        on_delete = "Cascade"
    )]
    Books,
    #[sea_orm(
        belongs_to = "super::users::Entity",
        from = "Column::UserId",
        to = "super::users::Column::Id",
        on_update = "NoAction",
        on_delete = "Cascade"
    )]
    Users,
}

impl Related<super::books::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Books.def()
    }
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Users.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
