use sea_orm::entity::prelude::*;
use chrono::{DateTime, Utc};

type DateTimeUtc = DateTime<Utc>;


#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq)]
#[sea_orm(table_name = "users")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: String,
    #[sea_orm(unique)]
    pub phone: Option<String>,
    #[sea_orm(unique)]
    pub employee_id: Option<String>,
    #[sea_orm(unique)]
    pub email: Option<String>,
    pub password_hash: String,
    pub role: Role,
    pub status: Status,
    pub created_at: Option<DateTimeUtc>,
    pub updated_at: Option<DateTimeUtc>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::borrowings::Entity")]
    Borrowings,
}

impl Related<super::borrowings::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Borrowings.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "role")]
pub enum Role {
    #[sea_orm(string_value = "user")]
    User,
    #[sea_orm(string_value = "admin")]
    Admin,
}
#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "status")]
pub enum Status {
    #[sea_orm(string_value = "active")]
    Active,
    #[sea_orm(string_value = "frozen")]
    Frozen,
    #[sea_orm(string_value = "deleted")]
    Deleted,
}